// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v6.30.1
// source: pbBase/MailDefine/MailDefine.proto

package MailDefine

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are enums.
// 邮件类型定义
type MAIL_TYPE int32

const (
	TOTAL        MAIL_TYPE = 0   //总计 服务端用
	SEND_PLAYER  MAIL_TYPE = -5  //客户端使用
	SEND_SERVICE MAIL_TYPE = -4  //客户端使用
	SEND         MAIL_TYPE = 1   //已发交易邮件
	SYSTEM       MAIL_TYPE = 2   //客服系统邮件
	MONEY        MAIL_TYPE = 4   //充值邮件
	TASK         MAIL_TYPE = 8   //任务邮件
	SERVICE      MAIL_TYPE = 16  //玩家联系客服的邮件
	PLAYER       MAIL_TYPE = 32  //玩家邮件
	BACK         MAIL_TYPE = 64  //回执/退回邮件
	RECEIPT      MAIL_TYPE = 128 //收据回执邮件
	CHARGE_FAIL  MAIL_TYPE = 256 //充值失败邮件
)

// Enum value maps for MAIL_TYPE.
var (
	MAIL_TYPE_name = map[int32]string{
		0:   "TOTAL",
		-5:  "SEND_PLAYER",
		-4:  "SEND_SERVICE",
		1:   "SEND",
		2:   "SYSTEM",
		4:   "MONEY",
		8:   "TASK",
		16:  "SERVICE",
		32:  "PLAYER",
		64:  "BACK",
		128: "RECEIPT",
		256: "CHARGE_FAIL",
	}
	MAIL_TYPE_value = map[string]int32{
		"TOTAL":        0,
		"SEND_PLAYER":  -5,
		"SEND_SERVICE": -4,
		"SEND":         1,
		"SYSTEM":       2,
		"MONEY":        4,
		"TASK":         8,
		"SERVICE":      16,
		"PLAYER":       32,
		"BACK":         64,
		"RECEIPT":      128,
		"CHARGE_FAIL":  256,
	}
)

func (x MAIL_TYPE) Enum() *MAIL_TYPE {
	p := new(MAIL_TYPE)
	*p = x
	return p
}

func (x MAIL_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MAIL_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_pbBase_MailDefine_MailDefine_proto_enumTypes[0].Descriptor()
}

func (MAIL_TYPE) Type() protoreflect.EnumType {
	return &file_pbBase_MailDefine_MailDefine_proto_enumTypes[0]
}

func (x MAIL_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MAIL_TYPE.Descriptor instead.
func (MAIL_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_pbBase_MailDefine_MailDefine_proto_rawDescGZIP(), []int{0}
}

// 邮件类型定义

type MAIL_STATUS int32

const (
	STATUS_UNKNOWN MAIL_STATUS = 0 //未知
	UNREAD_TEXT    MAIL_STATUS = 1 //未读纯文本
	UNREAD_ITEM    MAIL_STATUS = 2 //未读有附件
	UNREAD_TRADE   MAIL_STATUS = 3 //未读交易
	READ_TEXT      MAIL_STATUS = 4 //已读纯文本
	READ_ITEM      MAIL_STATUS = 5 //已读有附件
	READ_TRADE     MAIL_STATUS = 6 //已读交易
	READ_NO_ITEM   MAIL_STATUS = 7 //已读无附件
)

// Enum value maps for MAIL_STATUS.
var (
	MAIL_STATUS_name = map[int32]string{
		0: "STATUS_UNKNOWN",
		1: "UNREAD_TEXT",
		2: "UNREAD_ITEM",
		3: "UNREAD_TRADE",
		4: "READ_TEXT",
		5: "READ_ITEM",
		6: "READ_TRADE",
		7: "READ_NO_ITEM",
	}
	MAIL_STATUS_value = map[string]int32{
		"STATUS_UNKNOWN": 0,
		"UNREAD_TEXT":    1,
		"UNREAD_ITEM":    2,
		"UNREAD_TRADE":   3,
		"READ_TEXT":      4,
		"READ_ITEM":      5,
		"READ_TRADE":     6,
		"READ_NO_ITEM":   7,
	}
)

func (x MAIL_STATUS) Enum() *MAIL_STATUS {
	p := new(MAIL_STATUS)
	*p = x
	return p
}

func (x MAIL_STATUS) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MAIL_STATUS) Descriptor() protoreflect.EnumDescriptor {
	return file_pbBase_MailDefine_MailDefine_proto_enumTypes[1].Descriptor()
}

func (MAIL_STATUS) Type() protoreflect.EnumType {
	return &file_pbBase_MailDefine_MailDefine_proto_enumTypes[1]
}

func (x MAIL_STATUS) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MAIL_STATUS.Descriptor instead.
func (MAIL_STATUS) EnumDescriptor() ([]byte, []int) {
	return file_pbBase_MailDefine_MailDefine_proto_rawDescGZIP(), []int{1}
}

var File_pbBase_MailDefine_MailDefine_proto protoreflect.FileDescriptor

var file_pbBase_MailDefine_MailDefine_proto_rawDesc = []byte{
	0x0a, 0x22, 0x70, 0x62, 0x42, 0x61, 0x73, 0x65, 0x2f, 0x4d, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x66,
	0x69, 0x6e, 0x65, 0x2f, 0x4d, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x61, 0x69, 0x6c,
	0x44, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x2a, 0xb9, 0x01, 0x0a, 0x09, 0x4d, 0x41, 0x49, 0x4c, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x12, 0x09, 0x0a, 0x05, 0x54, 0x4f, 0x54, 0x41, 0x4c, 0x10, 0x00, 0x12,
	0x18, 0x0a, 0x0b, 0x53, 0x45, 0x4e, 0x44, 0x5f, 0x50, 0x4c, 0x41, 0x59, 0x45, 0x52, 0x10, 0xfb,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x12, 0x19, 0x0a, 0x0c, 0x53, 0x45, 0x4e,
	0x44, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x10, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x53, 0x45, 0x4e, 0x44, 0x10, 0x01, 0x12, 0x0a,
	0x0a, 0x06, 0x53, 0x59, 0x53, 0x54, 0x45, 0x4d, 0x10, 0x02, 0x12, 0x09, 0x0a, 0x05, 0x4d, 0x4f,
	0x4e, 0x45, 0x59, 0x10, 0x04, 0x12, 0x08, 0x0a, 0x04, 0x54, 0x41, 0x53, 0x4b, 0x10, 0x08, 0x12,
	0x0b, 0x0a, 0x07, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x10, 0x10, 0x12, 0x0a, 0x0a, 0x06,
	0x50, 0x4c, 0x41, 0x59, 0x45, 0x52, 0x10, 0x20, 0x12, 0x08, 0x0a, 0x04, 0x42, 0x41, 0x43, 0x4b,
	0x10, 0x40, 0x12, 0x0c, 0x0a, 0x07, 0x52, 0x45, 0x43, 0x45, 0x49, 0x50, 0x54, 0x10, 0x80, 0x01,
	0x12, 0x10, 0x0a, 0x0b, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x10,
	0x80, 0x02, 0x2a, 0x95, 0x01, 0x0a, 0x0b, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x4b,
	0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x4e, 0x52, 0x45, 0x41, 0x44,
	0x5f, 0x54, 0x45, 0x58, 0x54, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x4e, 0x52, 0x45, 0x41,
	0x44, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x10, 0x02, 0x12, 0x10, 0x0a, 0x0c, 0x55, 0x4e, 0x52, 0x45,
	0x41, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x44, 0x45, 0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09, 0x52, 0x45,
	0x41, 0x44, 0x5f, 0x54, 0x45, 0x58, 0x54, 0x10, 0x04, 0x12, 0x0d, 0x0a, 0x09, 0x52, 0x45, 0x41,
	0x44, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x10, 0x05, 0x12, 0x0e, 0x0a, 0x0a, 0x52, 0x45, 0x41, 0x44,
	0x5f, 0x54, 0x52, 0x41, 0x44, 0x45, 0x10, 0x06, 0x12, 0x10, 0x0a, 0x0c, 0x52, 0x45, 0x41, 0x44,
	0x5f, 0x4e, 0x4f, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x10, 0x07, 0x42, 0x2b, 0x5a, 0x29, 0x77, 0x6f,
	0x72, 0x6c, 0x64, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x42, 0x61, 0x73,
	0x65, 0x2f, 0x4d, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x3b, 0x4d, 0x61, 0x69,
	0x6c, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbBase_MailDefine_MailDefine_proto_rawDescOnce sync.Once
	file_pbBase_MailDefine_MailDefine_proto_rawDescData = file_pbBase_MailDefine_MailDefine_proto_rawDesc
)

func file_pbBase_MailDefine_MailDefine_proto_rawDescGZIP() []byte {
	file_pbBase_MailDefine_MailDefine_proto_rawDescOnce.Do(func() {
		file_pbBase_MailDefine_MailDefine_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbBase_MailDefine_MailDefine_proto_rawDescData)
	})
	return file_pbBase_MailDefine_MailDefine_proto_rawDescData
}

var file_pbBase_MailDefine_MailDefine_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_pbBase_MailDefine_MailDefine_proto_goTypes = []interface{}{
	(MAIL_TYPE)(0),   // 0: proto.MailDefine.MAIL_TYPE
	(MAIL_STATUS)(0), // 1: proto.MailDefine.MAIL_STATUS
}
var file_pbBase_MailDefine_MailDefine_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbBase_MailDefine_MailDefine_proto_init() }
func file_pbBase_MailDefine_MailDefine_proto_init() {
	if File_pbBase_MailDefine_MailDefine_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbBase_MailDefine_MailDefine_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbBase_MailDefine_MailDefine_proto_goTypes,
		DependencyIndexes: file_pbBase_MailDefine_MailDefine_proto_depIdxs,
		EnumInfos:         file_pbBase_MailDefine_MailDefine_proto_enumTypes,
	}.Build()
	File_pbBase_MailDefine_MailDefine_proto = out.File
	file_pbBase_MailDefine_MailDefine_proto_rawDesc = nil
	file_pbBase_MailDefine_MailDefine_proto_goTypes = nil
	file_pbBase_MailDefine_MailDefine_proto_depIdxs = nil
}
