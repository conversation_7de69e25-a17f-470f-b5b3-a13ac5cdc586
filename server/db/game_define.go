package db

import (
	"fmt"
	"world/base/env"
)

// 用户数据表-不分区
var USER = Def("user").Schema(map[string]bool{
	"username": true,
	"password": false,
})

// 区服数据表
var AREA = Def("area").Schema(map[string]bool{
	"id": true,
})

// 角色数据表-分区
var PLAYER *TableDef

// 邮件
var MAIL *TableDef

// 公共数据-不分区
var COMMON = Def("common").Schema(map[string]bool{
	"key": true,
})

func delayInit() {
	PLAYER = Def(fmt.Sprintf("player_%d", env.GetSid())).
		Schema(map[string]bool{
			"id":     false,
			"gameId": true,
			"name":   true,
		})
	MAIL = Def(fmt.Sprintf("mail_%d", env.GetSid())).
		Schema(map[string]bool{
			"id":         true,  // 邮件id
			"receiver":   false, // 收件人id
			"sender":     false, // 发件人id
			"typ":        false, // 邮件类型
			"status":     false, // 邮件状态
			"expireTime": false, // 过期时间
		}).Index(
		IndexDef{
			Fields: []IndexField{
				{Field: "receiver", Sort: 1},
				{Field: "status", Sort: 1},
			},
			Unique: false},
		IndexDef{
			Fields: []IndexField{
				{Field: "receiver", Sort: 1},
				{Field: "expireTime", Sort: -1},
			},
			Unique: false},
		IndexDef{
			Fields: []IndexField{
				{Field: "receiver", Sort: 1},
				{Field: "typ", Sort: 1},
			},
			Unique: false},
		IndexDef{
			Fields: []IndexField{
				{Field: "sender", Sort: 1},
				{Field: "typ", Sort: 1},
			},
			Unique: false},
		IndexDef{
			Fields: []IndexField{
				{Field: "sender", Sort: 1},
				{Field: "status", Sort: 1},
			},
			Unique: false},
		IndexDef{
			Fields: []IndexField{
				{Field: "receiver", Sort: 1},
				{Field: "typ", Sort: 1},
				{Field: "status", Sort: 1},
				{Field: "_id", Sort: -1},
			},
			Unique: false},
	)
}
