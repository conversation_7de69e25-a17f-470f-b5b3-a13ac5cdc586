package gameMgr

import (
	"context"
	"strings"
	"world/common/pbBase"
	"world/common/pbBase/MailDefine"
	"world/common/pbBase/Response"
	"world/common/pbCross"
	"world/common/pbGame"
	"world/common/pb_helper"
	"world/common/router"
	"world/db"
	"world/game/gameBase/event"
	"world/game/gameBase/gameStruct"
	ut "world/utils"
	"world/utils/serializer"

	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
	"github.com/spf13/cast"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var mailManagerLock deadlock.Once
var mailManager *MailManager

// Mail 邮件管理
func Mail() *MailManager {
	mailManagerLock.Do(func() {
		mailManager = &MailManager{}
		event.Require(mailManager)
	})
	return mailManager
}

type MailManager struct {
}

// 检查附件列表
func (m *MailManager) checkAppenDix(plr *gameStruct.Player, appendix []*pbBase.ItemData) Response.Code {
	if len(appendix) > 0 {
		bag := plr.BagModule()
		for _, item := range appendix {
			hasItem := bag.GetBagItem(int(item.GetSlotPos()))
			if hasItem == nil {
				return Response.E7002
			}
			if hasItem.GetQuantity() < int(item.GetQuantity()) {
				return Response.E7003
			}
		}
	}
	return Response.NoError
}

// PlayerMailByName 使用名称给玩家发送邮件
func (m *MailManager) PlayerMailByName(sender *gameStruct.Player, receiverName string, content string, money1, money3, reqMoney1, reqMoney3 int, appendix []*pbBase.ItemData) Response.Code {
	receiverName = strings.ReplaceAll(receiverName, " ", "")
	if receiverName == "" {
		return Response.E7001
	}
	for _, plr := range Player().GetAll().Items() {
		if plr.GetName() == receiverName {
			return m.PlayerMail(sender, plr.GetGameId(), content, receiverName, money1, money3, reqMoney1, reqMoney3, appendix)
		}
	}
	res := db.PLAYER.GetCollection().FindOne(context.Background(), bson.M{"name": receiverName}, options.FindOne().SetProjection(bson.M{"gameId": true, "_id": false}))
	var tmp bson.M
	err := res.Decode(&tmp)
	if err == mongo.ErrNoDocuments {
		return Response.E7001
	}
	gameId := cast.ToInt(tmp["gameId"])
	return m.PlayerMail(sender, gameId, content, receiverName, money1, money3, reqMoney1, reqMoney3, appendix)
}

// PlayerMail 给玩家发送邮件
func (m *MailManager) PlayerMail(sender *gameStruct.Player, receiver int, content, receiverName string, money1, money3, reqMoney1, reqMoney3 int, appendix []*pbBase.ItemData) Response.Code {
	if money1 < 0 || money3 < 0 || reqMoney1 < 0 || reqMoney3 < 0 {
		return Response.E7000
	}
	if sender.GetGameId() == receiver {
		return Response.E7000
	}
	if receiver == 0 {
		return Response.E7001
	}
	iAppendix := make([]*gameStruct.Item, 0)
	bag := sender.BagModule()
	for _, pbItem := range appendix {
		quantity := int(pbItem.GetQuantity())
		if quantity <= 0 {
			return Response.E7003
		}
		item := bag.GetBagItem(int(pbItem.GetSlotPos()))
		if item == nil {
			return Response.E7002
		}
		if item.GetQuantity() < quantity {
			return Response.E7003
		}
		iAppendix = append(iAppendix, item)
	}

	mail := gameStruct.NewMail(sender.GetGameId(), receiver, "", content, receiverName, sender.GetName(), money1, money3, reqMoney1, reqMoney3, iAppendix, nil, MailDefine.PLAYER)
	code := m.send(mail)
	if code == Response.NoError {
		// 处理附件物品
		for _, pbItem := range appendix {
			if i := bag.RemoveBagItemByPos(int(pbItem.GetSlotPos()), int(pbItem.GetQuantity())); i != 0 {
				log.Error("严重bug,附件物品移除失败:%v", i)
			}
		}
	}
	return code
}

func (m *MailManager) TaskMail(sender *gameStruct.Player, receiver int, title, content string, money1, money3, reqMoney1, reqMoney3 int, appendix []*pbBase.ItemData, selectItem []*pbBase.ItemData) {

}

func (m *MailManager) RechargeMail(sender *gameStruct.Player, receiver int, title, content string, money1, money3, reqMoney1, reqMoney3 int, appendix []*pbBase.ItemData, selectItem []*pbBase.ItemData) {

}

func (m *MailManager) SystemMail(sender *gameStruct.Player, receiver int, title, content string, money1, money3, reqMoney1, reqMoney3 int, appendix []*pbBase.ItemData, selectItem []*pbBase.ItemData) {

}

func (m *MailManager) BackMail(sender *gameStruct.Player, receiver int, title, content string, money1, money3, reqMoney1, reqMoney3 int, appendix []*pbBase.ItemData, selectItem []*pbBase.ItemData) {

}

func (m *MailManager) send(mail *gameStruct.Mail) Response.Code {
	if mail == nil {
		return Response.E7000
	}
	if mail.GetReceiver() <= 0 {
		return Response.E7001
	}

	switch mail.GetTyp() {
	case MailDefine.PLAYER:
	case MailDefine.TASK:
	case MailDefine.SYSTEM:
	case MailDefine.BACK:
	default:
		return Response.E7000
	}
	// 接收方不在线 则写入数据库
	receiverNodeId := Player().GetPlayerNodeIdByGameId(mail.GetReceiver())
	if receiverNodeId == "" {
		return m.saveMailToDatabase(mail)
	}
	// 否则就发送s2r消息
	result, err := Sender().SendMsgToNode(receiverNodeId, router.S2RNotifyNewMailMessage, &pbCross.S2R_NotifyNewMailMessage{Mail: mail.ToPb()})
	if err != nil {
		// 消息发送失败 就走数据库保存
		return m.saveMailToDatabase(mail)
	}
	// 检查S2R返回结果
	if result != nil {
		response := &pbCross.R2S_SimpleResponseMessage{}
		if err := pb_helper.ProtoUnMarshal(result, response); err == nil {
			switch response.GetCode() {
			case Response.E6001:
				return m.saveMailToDatabase(mail)
			case Response.NoError:
				return Response.NoError
			default:
				log.Info("目标节点处理邮件失败，回退到数据库存储，错误码:%v", response.GetCode())
				return m.saveMailToDatabase(mail)
			}
		} else {
			log.Error("解析S2R返回消息失败:%v", err)
		}
	}
	return Response.NoError
}

// ProcessNewMail 处理收到的新邮件（S2R消息处理）
func (m *MailManager) ProcessNewMail(mailPb *pbBase.Mail) Response.Code {
	// 验证邮件数据
	if mailPb == nil || mailPb.GetReceiver() <= 0 {
		return Response.E7000
	}
	// 检查目标玩家是否在当前节点在线
	receiverGameId := int(mailPb.GetReceiver())
	plr, exists := Player().TryGetPlayerByGameId(receiverGameId)
	if !exists {
		// 玩家不在当前节点，返回明确的错误码 让发送方处理后续
		log.Info("收到新邮件但目标玩家[%d]不在当前节点", receiverGameId)
		return Response.E6001 // 对方已经离线!
	}
	lock := Player().LockByUid(plr.GetId())
	defer ut.Unlock(lock)
	// 先更新内存状态
	mailCache := plr.GetMailCache()
	mail := gameStruct.NewMailByPb(mailPb)
	mailCache.AddMail(mail)
	//实时通知客户端
	Sender().SendTo(plr, router.S2CNewMailMessage, &pbGame.S2C_NewMailMessage{}, false)
	return Response.NoError
}

// GetPlayerMailList 获取玩家的邮件列表（分页）
func (m *MailManager) GetPlayerMailList(player *gameStruct.Player, mailType MailDefine.MAIL_TYPE, page int) ([]*gameStruct.Mail, int, Response.Code) {
	if player == nil {
		return nil, 0, Response.E7000
	}

	// 获取邮件缓存
	mailCache := player.GetMailCache()

	// 调用缓存的获取邮件列表方法
	mails, totalPages, err := mailCache.GetMailList(mailType, page)
	if err != nil {
		log.Error("获取玩家邮件列表失败，玩家ID:%s, 类型:%d, 页码:%d, 错误:%v",
			player.GetId(), mailType, page, err)
		return nil, 0, Response.E7000
	}

	return mails, totalPages, Response.NoError
}

// saveMailToDatabase 保存邮件到数据库
func (m *MailManager) saveMailToDatabase(mail *gameStruct.Mail) Response.Code {
	doc := serializer.ToBsonM(mail)
	_, err := db.MAIL.GetCollection().InsertOne(context.Background(), doc)
	if err != nil {
		log.Error("没有处理的错误--发送邮件时保存邮件到数据库出现错误:%v", err)
		return Response.E7004
	}
	return Response.NoError
}
