package gameStruct

import (
	"world/common/pbBase"
	"world/common/pbBase/MailDefine"
	ut "world/utils"

	"github.com/samber/lo"
)

func NewMail(sender, receiver int, title, content, toName, fromName string, money1, money3, reqMoney1, reqMoney3 int, appendix []*Item, selectItem []*Item, typ MailDefine.MAIL_TYPE) *Mail {
	id := ut.GenId()
	mail := &Mail{
		id:         id,
		receiver:   receiver,
		title:      title,
		content:    content,
		sender:     sender,
		appendix:   appendix,
		selectItem: selectItem,
		typ:        typ,
		reqMoney1:  reqMoney1,
		reqMoney3:  reqMoney3,
		money1:     money1,
		money3:     money3,
		expireTime: ut.Now() + ut.TIME_DAY*180,
		toName:     toName,
		fromName:   fromName,
	}
	mail.status = MailDefine.UNREAD_TEXT
	if len(appendix) > 0 || len(selectItem) > 0 {
		mail.status = MailDefine.UNREAD_ITEM
	}
	if mail.reqMoney1 > 0 || mail.reqMoney3 > 0 {
		mail.status = MailDefine.UNREAD_TRADE
	}
	return mail
}

// 从pb中创建
func NewMailByPb(mailPb *pbBase.Mail) *Mail {
	id := mailPb.GetId()
	receiver := int(mailPb.GetReceiver())
	sender := int(mailPb.GetSender())
	title := mailPb.GetTitle()
	content := mailPb.GetContent()
	typ := mailPb.GetType()
	status := mailPb.GetStatus()
	appendix := lo.Map(mailPb.GetAppendix(), func(item *pbBase.ItemData, index int) *Item { return NewItemByPb(item) })
	selectItem := lo.Map(mailPb.GetSelectItem(), func(item *pbBase.ItemData, index int) *Item { return NewItemByPb(item) })
	reqMoney1 := int(mailPb.GetReqMoney1())
	reqMoney3 := int(mailPb.GetReqMoney3())
	money1 := int(mailPb.GetMoney1())
	money3 := int(mailPb.GetMoney3())
	expireTime := mailPb.GetExpireTime()
	return &Mail{
		id:         id,
		receiver:   receiver,
		title:      title,
		content:    content,
		toName:     mailPb.GetReceiverName(),
		fromName:   mailPb.GetSenderName(),
		sender:     sender,
		appendix:   appendix,
		selectItem: selectItem,
		status:     status,
		typ:        typ,
		reqMoney1:  reqMoney1,
		reqMoney3:  reqMoney3,
		money1:     money1,
		money3:     money3,
		expireTime: expireTime,
		update:     true,
	}
}

type Mail struct {
	id         int64                  `marshal:"id,omitempty"`         // 邮件唯一id
	receiver   int                    `marshal:"receiver,omitempty"`   // 收件人id
	title      string                 `marshal:"title,omitempty"`      // 邮件标题
	content    string                 `marshal:"content,omitempty"`    // 邮件内容
	sender     int                    `marshal:"sender,omitempty"`     // 发件人id
	appendix   []*Item                `marshal:"reward,omitempty"`     // 奖励附件
	selectItem []*Item                `marshal:"selectItem,omitempty"` // 可选附件
	status     MailDefine.MAIL_STATUS `marshal:"status,omitempty"`     // 邮件状态
	typ        MailDefine.MAIL_TYPE   `marshal:"typ,omitempty"`        // 邮件类型
	expireTime int64                  `marshal:"expireTime,omitempty"` // 过期时间
	reqMoney1  int                    `marshal:"reqMoney1,omitempty"`  // 索取黄金
	reqMoney3  int                    `marshal:"reqMoney2,omitempty"`  // 索取铜币
	money1     int                    `marshal:"money1,omitempty"`     // 赠送黄金
	money3     int                    `marshal:"money2,omitempty"`     // 赠送铜币
	toName     string                 `marshal:"toName,omitempty"`     // 收件人名称 有必要存一下
	fromName   string                 `marshal:"fromName,omitempty"`   // 发件人名称 有必要存一下
	update     bool                   `marshal:"-"`                    // 需要db写入 这个字段最好不要手动更改值
}

func (m *Mail) GetId() int64                      { return m.id }
func (m *Mail) GetReceiver() int                  { return m.receiver }
func (m *Mail) GetTitle() string                  { return m.title }
func (m *Mail) GetContent() string                { return m.content }
func (m *Mail) GetSender() int                    { return m.sender }
func (m *Mail) GetAppendix() []*Item              { return m.appendix }
func (m *Mail) GetSelectItem() []*Item            { return m.selectItem }
func (m *Mail) GetStatus() MailDefine.MAIL_STATUS { return m.status }
func (m *Mail) GetTyp() MailDefine.MAIL_TYPE      { return m.typ }
func (m *Mail) GetExpireTime() int64              { return m.expireTime }
func (m *Mail) GetReqMoney1() int                 { return m.reqMoney1 }
func (m *Mail) GetReqMoney3() int                 { return m.reqMoney3 }
func (m *Mail) GetMoney1() int                    { return m.money1 }
func (m *Mail) GetMoney3() int                    { return m.money3 }

func (m *Mail) SetUpdate() { m.update = true }

func (m *Mail) ToPb() *pbBase.Mail {
	return &pbBase.Mail{
		Id:           m.id,
		Receiver:     int32(m.receiver),
		Title:        m.title,
		Content:      m.content,
		Sender:       int32(m.sender),
		ReceiverName: m.toName,
		SenderName:   m.fromName,
		Type:         m.typ,
		Status:       m.status,
		Appendix:     lo.Map(m.appendix, func(item *Item, index int) *pbBase.ItemData { return item.ToPb() }),
		SelectItem:   lo.Map(m.selectItem, func(item *Item, index int) *pbBase.ItemData { return item.ToPb() }),
		ExpireTime:   m.expireTime,
		ReqMoney1:    int32(m.reqMoney1),
		ReqMoney3:    int32(m.reqMoney3),
		Money1:       int32(m.money1),
		Money3:       int32(m.money3),
	}
}
