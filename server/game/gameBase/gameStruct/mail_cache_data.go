package gameStruct

import (
	"context"
	"fmt"
	"strconv"
	"time"
	"world/common/pbBase/MailDefine"
	"world/db"
	ut "world/utils"
	"world/utils/serializer"

	"github.com/huyangv/vmqant/log"
	"github.com/samber/lo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func NewMailCacheData(plr *Player) *MailCacheData {
	return &MailCacheData{plr: plr}
}

// 单个邮件类型的统计信息 - 支持所有邮件状态
type MailTypeStats struct {
	UnreadText  int `marshal:"-"` // 未读纯文本 (UNREAD_TEXT = 1)
	UnreadItem  int `marshal:"-"` // 未读有附件 (UNREAD_ITEM = 2)
	UnreadTrade int `marshal:"-"` // 未读交易 (UNREAD_TRADE = 3)
	ReadText    int `marshal:"-"` // 已读纯文本 (READ_TEXT = 4)
	ReadItem    int `marshal:"-"` // 已读有附件 (READ_ITEM = 5)
	ReadTrade   int `marshal:"-"` // 已读交易 (READ_TRADE = 6)
	ReadNoItem  int `marshal:"-"` // 已读无附件 (READ_NO_ITEM = 7)
}

// GetUnreadTotal 获取未读总数
func (s *MailTypeStats) GetUnreadTotal() int {
	return s.UnreadText + s.UnreadItem + s.UnreadTrade
}

// GetReadTotal 获取已读总数
func (s *MailTypeStats) GetReadTotal() int {
	return s.ReadText + s.ReadItem + s.ReadTrade + s.ReadNoItem
}

// GetTotal 获取总数
func (s *MailTypeStats) GetTotal() int {
	return s.GetUnreadTotal() + s.GetReadTotal()
}

// IsEmpty 检查是否为空
func (s *MailTypeStats) IsEmpty() bool {
	return s.GetTotal() == 0
}

// GetStatusCount 根据状态获取数量
func (s *MailTypeStats) GetStatusCount(status MailDefine.MAIL_STATUS) int {
	switch status {
	case MailDefine.UNREAD_TEXT:
		return s.UnreadText
	case MailDefine.UNREAD_ITEM:
		return s.UnreadItem
	case MailDefine.UNREAD_TRADE:
		return s.UnreadTrade
	case MailDefine.READ_TEXT:
		return s.ReadText
	case MailDefine.READ_ITEM:
		return s.ReadItem
	case MailDefine.READ_TRADE:
		return s.ReadTrade
	case MailDefine.READ_NO_ITEM:
		return s.ReadNoItem
	default:
		return 0
	}
}

// SetStatusCount 设置状态数量
func (s *MailTypeStats) SetStatusCount(status MailDefine.MAIL_STATUS, count int) {
	if count < 0 {
		count = 0
	}
	switch status {
	case MailDefine.UNREAD_TEXT:
		s.UnreadText = count
	case MailDefine.UNREAD_ITEM:
		s.UnreadItem = count
	case MailDefine.UNREAD_TRADE:
		s.UnreadTrade = count
	case MailDefine.READ_TEXT:
		s.ReadText = count
	case MailDefine.READ_ITEM:
		s.ReadItem = count
	case MailDefine.READ_TRADE:
		s.ReadTrade = count
	case MailDefine.READ_NO_ITEM:
		s.ReadNoItem = count
	}
}

// AddStatusCount 增加状态数量
func (s *MailTypeStats) AddStatusCount(status MailDefine.MAIL_STATUS, delta int) {
	current := s.GetStatusCount(status)
	s.SetStatusCount(status, current+delta)
}

type MailCacheData struct {
	plr     *Player                                 `marshal:"-"` // 玩家
	mails   []*Mail                                 `marshal:"-"` // 邮件数据
	numInfo map[MailDefine.MAIL_TYPE]*MailTypeStats `marshal:"-"` // 邮件数量信息
}

// HasUnread 检查是否有未读邮件（基于numInfo计算）
func (m *MailCacheData) HasUnread() bool {
	if m.numInfo == nil {
		return false
	}
	for _, stats := range m.numInfo {
		if stats.GetUnreadTotal() > 0 {
			return true
		}
	}
	return false
}

// AddMail 添加邮件 这里没考虑锁 一定要注意上层调用
func (m *MailCacheData) AddMail(mail *Mail) {
	// 增加类型-状态数量
	m.UpdateMailStatusCount(mail.typ, mail.status, 1)
	m.mails = append(m.mails, mail)
}

// RemoveMail 移除邮件 这里没考虑锁 一定要注意上层调用
func (m *MailCacheData) RemoveMail(mail *Mail) {
	_, index, _ := lo.FindIndexOf(m.mails, func(item *Mail) bool { return item == mail })
	if index != -1 {
		m.mails = append(m.mails[:index], m.mails[index+1:]...)
		// 减少类型-状态数量
		m.UpdateMailStatusCount(mail.typ, mail.status, -1)
	}
}

func (m *MailCacheData) GetNumInfo() map[MailDefine.MAIL_TYPE]*MailTypeStats {
	// 如果内存中已有数据，直接返回
	if m.numInfo != nil {
		return m.numInfo
	}

	sTime := time.Now()

	// 初始化结果map
	numInfo := make(map[MailDefine.MAIL_TYPE]*MailTypeStats)
	// 使用聚合查询一次性获取所有统计信息
	// 建议创建复合索引：db.mail.createIndex({"receiver": 1, "typ": 1, "status": 1})
	pipeline := mongo.Pipeline{
		// 第一阶段：过滤指定接收者的邮件
		{{Key: "$match", Value: bson.M{"receiver": m.plr.GetGameId()}}},
		// 第二阶段：按邮件类型和状态分组统计
		{{Key: "$group", Value: bson.M{
			"_id": bson.M{
				"typ":    "$typ",
				"status": "$status",
			},
			"count": bson.M{"$sum": 1},
		}}},
	}

	cursor, err := db.MAIL.GetCollection().Aggregate(context.Background(), pipeline)
	if err != nil {
		log.Warning("聚合查询邮件统计失败: %v", err)
		return numInfo
	}
	defer cursor.Close(context.Background())

	// 解析聚合结果
	var results []bson.M
	if err = cursor.All(context.Background(), &results); err != nil {
		log.Warning("解析聚合结果失败: %v", err)
		return numInfo
	}

	// 处理聚合结果
	for _, result := range results {
		// 获取_id字段
		idField, ok := result["_id"].(bson.M)
		if !ok {
			log.Warning("聚合结果_id字段类型错误: %T", result["_id"])
			continue
		}

		// 获取邮件类型ID
		typId, ok := idField["typ"].(int32)
		if !ok {
			log.Warning("邮件类型ID类型错误: %T, 值: %v", idField["typ"], idField["typ"])
			continue
		}

		// 获取状态ID
		statusId, ok := idField["status"].(int32)
		if !ok {
			log.Warning("邮件状态ID类型错误: %T, 值: %v", idField["status"], idField["status"])
			continue
		}

		// 获取数量
		count, ok := result["count"].(int32)
		if !ok {
			log.Warning("邮件数量类型错误: %T, 值: %v", result["count"], result["count"])
			continue
		}

		mailType := MailDefine.MAIL_TYPE(typId)
		status := MailDefine.MAIL_STATUS(statusId)

		// 跳过TOTAL类型和未知状态
		if mailType == MailDefine.TOTAL || status == MailDefine.STATUS_UNKNOWN {
			continue
		}

		// 验证数量的合理性
		if count < 0 {
			log.Warning("邮件数量为负数，玩家ID:%s, 类型:%d, 状态:%d, 数量:%d",
				m.plr.GetId(), mailType, status, count)
			continue
		}

		// 确保stats对象存在
		stats := numInfo[mailType]
		if stats == nil {
			stats = &MailTypeStats{}
			numInfo[mailType] = stats
		}

		// 设置对应状态的数量
		stats.SetStatusCount(status, int(count))
	}

	// 缓存结果
	m.numInfo = numInfo

	// 保存到Redis缓存
	m.saveToRedis()

	log.Debug("GetNumInfo 完成，类型数量:%d, 耗时:%.3fs", len(numInfo), time.Since(sTime).Seconds())
	return m.numInfo
}

// save 批量保存邮件数据到数据库
func (m *MailCacheData) save() {
	if m.plr == nil {
		log.Error("严重错误，玩家为空")
		return
	}
	if len(m.mails) == 0 {
		return
	}
	// 收集需要更新的邮件
	needUpdateMails := make([]*Mail, 0)
	for _, mail := range m.mails {
		if mail.update {
			needUpdateMails = append(needUpdateMails, mail)
		}
	}
	if len(needUpdateMails) == 0 {
		return
	}
	unique := m.plr.GetId()
	tm := ut.LPLock(unique)
	defer ut.LPUnlock(tm)

	// 批量大小设置为50，基于邮件数据大小分析
	// 单个邮件估算2-10KB，50个邮件约100-500KB，安全范围内
	const batchSize = 50
	sTime := time.Now()
	totalUpdated := 0
	// 分批处理
	for i := 0; i < len(needUpdateMails); i += batchSize {
		end := i + batchSize
		if end > len(needUpdateMails) {
			end = len(needUpdateMails)
		}

		batch := needUpdateMails[i:end]
		if err := m.saveBatch(batch); err != nil {
			log.Error("批量保存邮件失败，玩家ID:%s, 批次:%d-%d, 错误:%v",
				m.plr.GetId(), i, end-1, err)
			continue
		}

		// 清除update标记
		for _, mail := range batch {
			mail.update = false
		}
		totalUpdated += len(batch)
	}
	log.Debug("邮件批量保存完成，玩家ID:%s, 更新数量:%d, 耗时:%.3fs",
		m.plr.GetId(), totalUpdated, time.Since(sTime).Seconds())
}

// saveBatch 批量保存一批邮件到数据库
func (m *MailCacheData) saveBatch(mails []*Mail) error {
	if len(mails) == 0 {
		return nil
	}
	operations := make([]mongo.WriteModel, 0, len(mails))
	for _, mail := range mails {
		filter := bson.M{"id": mail.id}
		doc := serializer.ToBsonM(mail)
		// 处理插入和更新
		operation := mongo.NewReplaceOneModel()
		operation.SetFilter(filter)
		operation.SetReplacement(doc)
		operation.SetUpsert(true)
		operations = append(operations, operation)
	}
	// 设置批量写入选项 无序执行提高性能
	opts := options.BulkWrite().SetOrdered(false)
	// 执行批量写入
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	_, err := db.MAIL.GetCollection().BulkWrite(ctx, operations, opts)
	return err
}

// Redis缓存相关方法

// loadFromRedis 从Redis加载邮件缓存数据
func (m *MailCacheData) loadFromRedis() bool {
	if m.plr == nil {
		return false
	}

	redisKey := db.RedisKeyMailCache(m.plr.GetGameId())
	ctx := context.Background()

	// 获取所有邮件类型的统计数据
	mailTypes := []MailDefine.MAIL_TYPE{
		MailDefine.SEND, MailDefine.SYSTEM, MailDefine.MONEY, MailDefine.TASK,
		MailDefine.SERVICE, MailDefine.PLAYER, MailDefine.BACK, MailDefine.RECEIPT, MailDefine.CHARGE_FAIL,
	}

	// 所有可能的状态（除了STATUS_UNKNOWN）
	statuses := []MailDefine.MAIL_STATUS{
		MailDefine.UNREAD_TEXT, MailDefine.UNREAD_ITEM, MailDefine.UNREAD_TRADE,
		MailDefine.READ_TEXT, MailDefine.READ_ITEM, MailDefine.READ_TRADE, MailDefine.READ_NO_ITEM,
	}

	// 构建要获取的字段列表
	fields := make([]string, 0, len(mailTypes)*len(statuses))
	for _, mailType := range mailTypes {
		for _, status := range statuses {
			fields = append(fields, fmt.Sprintf("type_%d_status_%d", mailType, status))
		}
	}

	// 使用HMGET一次性获取所有字段
	result := db.GetRedis().HMGet(ctx, redisKey, fields...)
	if result.Err() != nil {
		log.Warning("从Redis加载邮件缓存失败，玩家ID:%s, 错误:%v", m.plr.GetId(), result.Err())
		return false
	}

	values := result.Val()
	if len(values) == 0 {
		return false
	}

	// 解析数据
	numInfo := make(map[MailDefine.MAIL_TYPE]*MailTypeStats)

	for i, mailType := range mailTypes {
		stats := &MailTypeStats{}
		hasTypeData := false

		for j, status := range statuses {
			idx := i*len(statuses) + j
			if idx >= len(values) || values[idx] == nil {
				continue
			}

			if countStr, ok := values[idx].(string); ok {
				if count, err := strconv.Atoi(countStr); err == nil && count > 0 {
					stats.SetStatusCount(status, count)
					hasTypeData = true
				}
			}
		}

		// 只有当该类型有数据时才添加到map中
		if hasTypeData {
			numInfo[mailType] = stats
		}
	}

	m.numInfo = numInfo
	return true
}

// saveToRedis 保存邮件缓存数据到Redis
func (m *MailCacheData) saveToRedis() error {
	if m.plr == nil || m.numInfo == nil {
		return nil
	}

	redisKey := db.RedisKeyMailCache(m.plr.GetGameId())
	ctx := context.Background()

	// 构建要设置的字段
	fields := make([]interface{}, 0)

	// 所有可能的状态（除了STATUS_UNKNOWN）
	statuses := []MailDefine.MAIL_STATUS{
		MailDefine.UNREAD_TEXT, MailDefine.UNREAD_ITEM, MailDefine.UNREAD_TRADE,
		MailDefine.READ_TEXT, MailDefine.READ_ITEM, MailDefine.READ_TRADE, MailDefine.READ_NO_ITEM,
	}

	for mailType, stats := range m.numInfo {
		for _, status := range statuses {
			count := stats.GetStatusCount(status)
			if count > 0 {
				field := fmt.Sprintf("type_%d_status_%d", mailType, status)
				fields = append(fields, field, fmt.Sprintf("%d", count))
			}
		}
	}

	if len(fields) == 0 {
		return nil
	}

	// 使用HMSET设置所有字段
	result := db.GetRedis().HMSet(ctx, redisKey, fields...)
	if result.Err() != nil {
		log.Warning("保存邮件缓存到Redis失败，玩家ID:%s, 错误:%v", m.plr.GetId(), result.Err())
		return result.Err()
	}

	log.Debug("保存邮件缓存到Redis成功，玩家ID:%s, 字段数量:%d", m.plr.GetId(), len(fields)/2)
	return nil
}

// UpdateMailStatusCount 更新某个类型邮件的某个状态的数量（在线玩家）
func (m *MailCacheData) UpdateMailStatusCount(mailType MailDefine.MAIL_TYPE, status MailDefine.MAIL_STATUS, delta int) {
	if m.numInfo == nil {
		m.numInfo = make(map[MailDefine.MAIL_TYPE]*MailTypeStats)
	}

	stats := m.numInfo[mailType]
	if stats == nil {
		stats = &MailTypeStats{}
		m.numInfo[mailType] = stats
	}

	// 更新指定状态的数量
	currentCount := stats.GetStatusCount(status)
	newCount := currentCount + delta
	stats.SetStatusCount(status, newCount)

	// 如果该类型邮件数量为0，从map中删除
	if stats.IsEmpty() {
		delete(m.numInfo, mailType)
	}

	// 同步到Redis
	m.saveToRedis()
}

// UpdatePlayerMailStatusCountRedis 更新某个玩家的某个类型邮件的某个状态的数量（直接操作Redis，用于离线玩家）
func UpdatePlayerMailStatusCountRedis(gameId int, mailType MailDefine.MAIL_TYPE, status MailDefine.MAIL_STATUS, delta int) error {
	redisKey := db.RedisKeyMailCache(gameId)
	ctx := context.Background()

	fieldName := fmt.Sprintf("type_%d_status_%d", mailType, status)

	// 使用Lua脚本保证原子性
	script := `
		local key = KEYS[1]
		local field = ARGV[1]
		local delta = tonumber(ARGV[2])

		local current = redis.call("HGET", key, field)
		if current == false then
			current = 0
		else
			current = tonumber(current)
		end

		local newValue = current + delta
		if newValue < 0 then
			newValue = 0
		end

		if newValue == 0 then
			redis.call("HDEL", key, field)
		else
			redis.call("HSET", key, field, newValue)
		end

		return newValue
	`

	result := db.GetRedis().Eval(ctx, script, []string{redisKey}, fieldName, fmt.Sprintf("%d", delta))
	if result.Err() != nil {
		log.Warning("更新玩家邮件状态计数失败，玩家ID:%s, 类型:%d, 状态:%d, 错误:%v", gameId, mailType, status, result.Err())
		return result.Err()
	}

	log.Debug("更新玩家邮件状态计数成功，玩家ID:%s, 类型:%d, 状态:%d, 字段:%s, 变化:%d",
		gameId, mailType, status, fieldName, delta)
	return nil
}

// GetMailList 获取指定类型的邮件列表（分页）
func (m *MailCacheData) GetMailList(mailType MailDefine.MAIL_TYPE, page int) ([]*Mail, int, error) {
	if m.plr == nil {
		return nil, 0, fmt.Errorf("玩家为空")
	}

	// 每页固定50条
	const pageSize = 50

	// 获取邮件统计信息
	numInfo := m.GetNumInfo()
	stats, exists := numInfo[mailType]
	if !exists || stats.IsEmpty() {
		// 该类型没有邮件
		return []*Mail{}, 0, nil
	}

	// 计算总数量和总页数
	totalCount := stats.GetTotal()
	totalPages := (totalCount + pageSize - 1) / pageSize

	// 检查页码有效性
	if page < 1 || page > totalPages {
		return []*Mail{}, totalPages, nil
	}

	// 从数据库查询邮件
	mails, err := m.queryMailsFromDB(mailType, page, pageSize)
	if err != nil {
		log.Error("查询邮件失败，玩家ID:%s, 类型:%d, 页码:%d, 错误:%v",
			m.plr.GetId(), mailType, page, err)
		return nil, totalPages, err
	}

	return mails, totalPages, nil
}

// queryMailsFromDB 从数据库查询邮件
func (m *MailCacheData) queryMailsFromDB(mailType MailDefine.MAIL_TYPE, page, pageSize int) ([]*Mail, error) {
	sTime := time.Now()

	// 构建查询条件
	filter := bson.M{
		"receiver": m.plr.GetGameId(),
		"typ":      mailType,
	}

	// 设置查询选项：按id降序排序（新邮件在前），分页
	opts := options.Find().
		SetSort(bson.M{"id": -1}).
		SetSkip(int64((page - 1) * pageSize)).
		SetLimit(int64(pageSize))

	// 执行查询
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	cursor, err := db.MAIL.GetCollection().Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("数据库查询失败: %v", err)
	}
	defer cursor.Close(ctx)

	// 解析结果
	var results []bson.M
	if err = cursor.All(ctx, &results); err != nil {
		return nil, fmt.Errorf("解析查询结果失败: %v", err)
	}

	// 转换为Mail对象
	mails := make([]*Mail, 0, len(results))
	for _, result := range results {
		mail := &Mail{}
		// 先将bson.M转换为BSON字节数组，再反序列化
		data, err := bson.Marshal(result)
		if err != nil {
			log.Warning("邮件数据序列化失败，跳过该邮件: %v", err)
			continue
		}
		if err := serializer.UnmarshalBSON(data, mail); err != nil {
			log.Warning("邮件数据反序列化失败，跳过该邮件: %v", err)
			continue
		}
		mails = append(mails, mail)
	}

	log.Debug("查询邮件完成，玩家ID:%s, 类型:%d, 页码:%d, 数量:%d, 耗时:%.3fs",
		m.plr.GetId(), mailType, page, len(mails), time.Since(sTime).Seconds())

	return mails, nil
}
