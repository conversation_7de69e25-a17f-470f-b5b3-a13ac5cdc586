package mail

import (
	"world/common/pbBase/MailDefine"
	"world/common/pbBase/Response"
	"world/common/pbGame"
	"world/game/gameBase/gameStruct"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// C2sMailDetailMessageHandler 获取邮件详情
func C2sMailDetailMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(player *gameStruct.Player, msg *pbGame.C2S_MailDetailMessage) protoreflect.ProtoMessage {
		id := msg.GetId()
		cache := player.GetMailCache()
		var vo *gameStruct.Mail
		if cache != nil {
			typ := msg.GetType()
			vo = cache.GetMailById(id, typ)
			status := vo.GetStatus()
			switch status {
			case MailDefine.UNREAD_TEXT:
				fallthrough
			case MailDefine.UNREAD_ITEM:
				fallthrough
			case MailDefine.UNREAD_TRADE:
				vo.Read()
				cache.ChangeMailStatusCount(typ, status, vo.GetStatus(), 1)
			}

		}
		return &pbGame.S2C_MailDetailMessage{
			Code: Response.NoError,
			Mail: vo.ToPbDetail(),
		}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
