package mail

import (
	"world/common/pbBase/Response"
	"world/common/pbGame"
	"world/game/gameBase/gameStruct"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// C2sMailDetailMessageHandler 获取邮件详情
func C2sMailDetailMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(player *gameStruct.Player, msg *pbGame.C2S_MailDetailMessage) protoreflect.ProtoMessage {
		id := msg.GetId()
		cache := player.GetMailCache()
		var vo *gameStruct.Mail
		if cache != nil {
			vo = cache.GetMailById(id)
			vo.Read()
		}
		return &pbGame.S2C_MailDetailMessage{
			Code: Response.NoError,
			Mail: vo.ToPbDetail(),
		}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
