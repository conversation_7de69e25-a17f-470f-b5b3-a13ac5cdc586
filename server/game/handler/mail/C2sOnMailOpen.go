package mail

import (
	"world/common/pbBase"
	"world/common/pbGame"
	"world/game/gameBase/gameStruct"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// C2sOnMailOpenMessageHandler 打开邮件界面时获取部分信息
func C2sOnMailOpenMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(player *gameStruct.Player, msg *pbGame.C2S_OnMailOpenMessage) protoreflect.ProtoMessage {
		cache := player.GetMailCache()
		info := cache.GetNumInfo()
		outInfo := make([]*pbBase.MailSimpleNumInfo, 0)

		for typ, stats := range info {
			// 创建邮件类型的统计信息
			outInfo = append(outInfo, &pbBase.MailSimpleNumInfo{
				Type:   typ,
				Unread: int32(stats.GetUnreadTotal()),
				Read:   int32(stats.GetReadTotal()),
			})
		}

		return &pbGame.S2C_OnMailOpenMessage{SimpleNumInfo: outInfo}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
