syntax = "proto3";

package proto;

option go_package = "world/common/pbBase;pbBase";

import "pbBase/AreaLineState/AreaLineState.proto";

import "pbBase/ConditionType/ConditionType.proto";

import "pbBase/MyDefine/MyDefine.proto";

import "pbBase/BattleDefine/BattleDefine.proto";

import "pbBase/MailDefine/MailDefine.proto";

//After are structs.
// 登录结果信息
message LoginResult{
  string id = 1; //用户id
  string token = 2; //会话凭证
}

// 区服信息
message AreaLine{
  int32 id = 1; //区服id
  string name = 2; //区服名称
  int32 openTime = 3; //区服名称
  proto.AreaLineState.Type state = 4; //区服状态
  int32 actorCount = 5; //拥有角色数量
}

// 跨节点简要传递玩家数据
message CrossSimplePlayer{
  string from = 1; //发送方，即玩家当前所在节点
  string id = 2; //玩家用户id
  int32 gameId = 3; //玩家游戏id
  string name = 4; //玩家名称
  int32 mapId = 5; //所处地图id
  int32 x = 6; //所处地图x
  int32 y = 7; //所处地图y
  int64 icon1 = 8; //icon1
  int64 icon2 = 9; //icon2
  int64 icon3 = 10; //icon3
  int32 level = 11; //等级
  int32 level2 = 12; //传奇等级
  string title = 13; //称号
  int64 setting = 14; //设置
  int64 status = 15; //状态
  int32 mode = 16; //设置
  string shopName = 17; //摆摊名称
  string countryName = 18; //国家名称
  int32 vipLv = 19; //vip等级
  int32 vipLvMax = 20; //历史最高vip等级
  CrossSimplePet pet = 21; //宠物数据
}

// 跨节点简要传递宠物数据
message CrossSimplePet{
  int32 cfgId = 1; //宠物配置id
  int64 id = 2; //宠物id
  string name = 3; //自定义名字，可能存在
  int64 age = 4; //寿命
}

// 玩家事件数据
message PlayerEvent{
  int32 eventId = 1; //事件id
  int32 eventType = 2; //事件类型
  string message = 3; //事件消息
  string extraInfo = 4; //额外信息
  int64 expireTime = 5; //过期时间
  CrossSimplePlayer player = 6; //发起者
}

// 简要玩家数据,用于拉取角色列表时使用
message SimplePlayerInfo{
  int32 id = 1; //玩家id
  int32 mapId = 2; //当前所处地图
  int32 x = 3; //所处地图x
  int32 y = 4; //所处地图y
  string name = 5; //玩家名称
  AttrData attr = 6; //属性
  int64 deleteEndTime = 7; //删除计时
  OfflineTaskData offlineTask = 8; //离线任务数据
}

// 玩家数据,玩家进入游戏时推送
message PlayerInfo{
  int32 id = 1; //玩家id
  int32 mapId = 2; //当前所处地图
  int32 x = 3; //所处地图x
  int32 y = 4; //所处地图y
  string name = 5; //玩家名称
  int64 setting = 6; //设置
  int32 mode = 7; //设置
  AttrData attr = 8; //属性
  BagData bag = 9; //背包
  TaskData task = 10; //任务
  repeated int32 itemSetData = 11; //玩家套装数据
  SkillData skill = 12; //技能
  int64 petId = 13; //上阵的宠物
  bool unreadMail = 14; //是否有未读邮件
}

// 属性模块
message AttrData{
  int64 icon1 = 1; //icon1
  int64 icon2 = 2; //icon2
  int64 icon3 = 3; //icon3
  int64 status = 4; //状态
  int32 level = 5; //等级
  int32 level2 = 6; //传奇等级
  int32 exp = 7; //普通经验
  int32 exp2 = 8; //传奇经验
  int32 vipLv = 9; //vip等级
  int32 vipLvMax = 10; //历史最高vip等级
  int32 cp = 11; //未使用的技能点
  int32 str = 12; //力量
  int32 agi = 13; //敏捷
  int32 con = 14; //体质
  int32 ilt = 15; //智力
  int32 wis = 16; //感知
  int32 hp = 17; //血
  int32 mp = 18; //蓝
}

// 背包数据
message BagData{
  int32 money1 = 1; //黄金
  int32 money2 = 2; //金叶
  int32 money3 = 3; //铜币
  int32 bagSize = 4; //背包格子数量
  int32 selfStoreSize = 5; //个人仓库购买数量
  map<int32,ItemData> store = 6; //物品数据
}

// 物品数据
message PetData{
  int32 cfgId = 1; //宠物配置id
  string name = 2; //自定义名字，可能存在
  int32 grow = 3; //成长值
  int32 learn = 4; //领悟值
  int32 growLevel = 5; //成长等级
  AttrData attr = 6; //属性
  SkillData skill = 7; //技能
  int64 age = 8; //寿命
  int32 growExp = 9; //成长经验
  int64 id = 10; //宠物id
}

// 物品数据
message ItemData{
  int32 id = 1; //id
  int32 slotPos = 2; //位置
  int32 quantity = 3; //数量
  int32 status = 4; //状态
  PowerData power1 = 5; //基础属性1
  PowerData power2 = 6; //基础属性2
  PowerData power3 = 7; //基础属性3
  PowerData bindPower1 = 8; //绑定属性1
  PowerData bindPower2 = 9; //绑定属性2
  PowerData power4 = 10; //进阶属性1
  PowerData power5 = 11; //进阶属性2
  PowerData power6 = 12; //进阶属性3
  PowerData power7 = 13; //进阶属性4
  PowerData enchantPower1 = 14; //附魔1
  PowerData enchantPower2 = 15; //附魔2
  int32 durability = 16; //耐久
  int32 attachDone = 17; //宝石镶嵌数量
  PowerData attachPower = 18; //宝石属性
  int64 expireTime = 19; //过期时间
  int32 star = 20; //普通升星数量
  int32 upgradeStar = 21; //进阶升星数量
  int64 petId = 22; //宠物唯一id
  PetData petItem = 23; //宠物数据
  map<string,string> extra = 24; //扩展信息
}

// 条件数据
message Condition{
  proto.ConditionType.Type type = 1; //类型
  int32 id = 2; //id
  int32 num = 3; //需求数量
  string extra = 4; //扩展数据
}

// 任务模块数据
message TaskData{
  bytes taskStatus = 1; //物品数据
  repeated TaskInfo tasks = 2; //物品数据
}

// 任务数据
message TaskInfo{
  int32 id = 1; //物品数据
  repeated Condition cond = 2; //条件详情
}

// 属性数据
message PowerData{
  proto.MyDefine.POWER type = 1; //属性类型
  int32 value = 2; //属性值
}

// 离线任务数据
message OfflineTaskData{
}

// 点位数据
message Point{
  int32 x = 1; //x
  int32 y = 2; //y
}

// 技能模块数据
message SkillData{
  int32 sp = 1; //技能点
  map<int32,SkillInfo> list = 2; //技能列表数据
  int32 cnt = 3; //技能槽数量
  int32 activeAutoSkillId = 4; //自动释放的主动技能id
  repeated int32 autoSkillId = 5; //自动释放的自动技能id
}

// 技能信息
message SkillInfo{
  int32 id = 1; //技能id
  int32 baseLevel = 2; //技能基础等级
  int32 addLevel = 3; //技能增加等级
  bool isLearn = 4; //是不是学习而来
}

// 使用物品响应-宠物潜能石
message ItemUseResultPetAddSkillItem{
  repeated int32 old = 1; //原本拥有的潜能技能id
  repeated SkillInfo new = 2; //新的潜能技能
}

// 战斗出招数据结构体
message BattlePlanObj{
  proto.BattleDefine.Plan type = 1; //出招类型
  int32 position = 2; //目标位置
  int32 extra = 3; //如果是使用技能,技能id;如果是使用物品,物品id
}

// 邮件简单信息
message MailSimpleNumInfo{
  int32 index = 1; //类型
  int32 unread = 2; //未读
  int32 read = 3; //已读
}

// 邮件数据
message Mail{
  int64 id = 1; //邮件id
  int32 receiver = 2; //接收者id
  string title = 3; //标题
  string content = 4; //内容
  string receiverName = 5; //接收者名称
  string senderName = 6; //发送者名称
  int32 sender = 7; //发送者id
  proto.MailDefine.MAIL_TYPE type = 8; //类型
  proto.MailDefine.MAIL_STATUS status = 9; //状态
  repeated ItemData appendix = 10; //附件
  repeated ItemData selectItem = 11; //可选列表
  int64 expireTime = 12; //过期时间
  int32 reqMoney1 = 13; //索要黄金
  int32 reqMoney3 = 14; //索要铜币
  int32 money1 = 15; //赠送黄金
  int32 money3 = 16; //赠送铜币
}

