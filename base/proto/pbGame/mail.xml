<?xml version="1.0" encoding="UTF-8" ?>
<messages package="world/common/pbGame">
    <message type="C2S" name="OnMailOpen" module="game" explain="打开邮件界面时获取部分信息">
    </message>
    <message type="S2C" name="OnMailOpen" module="game" explain="打开邮件界面时获取部分信息">
        <array class="pbBase/struct.MailSimpleNumInfo" name="simpleNumInfo" explain=""/>
    </message>
    <message type="C2S" name="SendMail" module="game" explain="发送邮件">
        <field class="string" name="content" explain="内容"/>
        <field class="int32" name="money1" explain="赠送黄金"/>
        <field class="int32" name="money3" explain="赠送铜币"/>
        <field class="int32" name="reqMoney1" explain="索取黄金"/>
        <field class="int32" name="reqMoney3" explain="索取铜币"/>
        <array class="pbBase/struct.ItemData" name="appendix" explain="附件"/>
        <field class="int32" name="toId" explain="接收者id"/>
        <field class="string" name="toName" explain="接收者名称"/>
    </message>
    <message type="S2C" name="SendMail" module="game" explain="发送邮件">
        <field class="pbBase/Response/Response.Code" name="code" explain="响应码"/>
    </message>
    <message type="C2S" name="MailList" module="game" explain="获取邮件列表">
        <field class="int32" name="type" explain="类型"/>
        <field class="int32" name="pageSize" explain="每页大小"/>
        <field class="int32" name="page" explain="页码"/>
    </message>
    <message type="S2C" name="MailList" module="game" explain="获取邮件列表">
        <field class="pbBase/Response/Response.Code" name="code" explain="响应码"/>
        <array class="pbBase/struct.Mail" name="mailList" explain="邮件列表"/>
        <field class="int32" name="totalPage" explain="总页数"/>
    </message>
</messages>