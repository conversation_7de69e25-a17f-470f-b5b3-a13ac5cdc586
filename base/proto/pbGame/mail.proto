syntax = "proto3";

package proto;

option go_package = "world/common/pbGame;pbGame";

import "pbBase/struct.proto";

import "pbBase/Response/Response.proto";

//After are messages.
message C2S_OnMailOpenMessage {
}
message S2C_OnMailOpenMessage {
  repeated MailSimpleNumInfo simpleNumInfo = 1; //
}
message C2S_SendMailMessage {
  string content = 1; //内容
  int32 money1 = 2; //赠送黄金
  int32 money3 = 3; //赠送铜币
  int32 reqMoney1 = 4; //索取黄金
  int32 reqMoney3 = 5; //索取铜币
  repeated ItemData appendix = 6; //附件
  int32 toId = 7; //接收者id
  string toName = 8; //接收者名称
}
message S2C_SendMailMessage {
  proto.Response.Code code = 1; //响应码
}
message C2S_MailListMessage {
  int32 type = 1; //类型
  int32 pageSize = 2; //每页大小
  int32 page = 3; //页码
}
message S2C_MailListMessage {
  proto.Response.Code code = 1; //响应码
  repeated Mail mailList = 2; //邮件列表
  int32 totalCount = 3; //总页数
}
message C2S_MailDetailMessage {
  int64 id = 1; //邮件id
}
message S2C_MailDetailMessage {
  proto.Response.Code code = 1; //响应码
  Mail mail = 2; //邮件详情
}
